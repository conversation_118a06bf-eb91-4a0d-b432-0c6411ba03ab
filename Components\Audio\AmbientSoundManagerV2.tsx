import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

// 🔧 CISCO: Nouveau système audio corrigé - correspondance exacte avec les dossiers
interface AmbientSoundManagerV2Props {
  skyMode: string;
  enabled?: boolean;
  volume?: number; // 0 to 1
}

// 🎵 Configuration des sons par mode - correspondance exacte avec les dossiers
const SOUND_CONFIG: Record<string, {
  sounds: string[]; // Plusieurs sons possibles par mode
  volume: number;
  folder: string;
  fadeInDuration?: number;
  fadeOutDuration?: number;
}> = {
    // 🌙 Nuit profonde - 2 sons disponibles
    night: { 
      sounds: ['hibou-molkom.mp3', 'night-atmosphere-with-crickets-374652.mp3'], 
      volume: 0.6, 
      folder: 'nuit-profonde', 
      fadeInDuration: 5000, 
      fadeOutDuration: 5000 
    },
    
    // 🌃 Crépuscule - 2 sons disponibles  
    dusk: { 
      sounds: ['cricket-single.mp3', 'merle-blackbird.mp3'], 
      volume: 0.4, 
      folder: 'crepuscule', 
      fadeInDuration: 5000, 
      fadeOutDuration: 5000 
    },
    
    // 🌅 Aube - 1 son disponible
    dawn: { 
      sounds: ['village_morning_birds_roosters.mp3'], 
      volume: 0.5, 
      folder: 'aube', 
      fadeInDuration: 5000, 
      fadeOutDuration: 5000 
    },
    
    // 🌄 Lever du soleil - 1 son disponible
    sunrise: { 
      sounds: ['blackbird.mp3'], 
      volume: 0.6, 
      folder: 'lever-soleil', 
      fadeInDuration: 5000, 
      fadeOutDuration: 5000 
    },
    
    // 🌅 Matin - 2 sons disponibles
    morning: { 
      sounds: ['insect_bee_fly.mp3', 'morning-birdsong.mp3'], 
      volume: 0.7, 
      folder: 'matin', 
      fadeInDuration: 6000, 
      fadeOutDuration: 6000 
    },
    
    // ☀️ Midi - 1 son disponible
    midday: { 
      sounds: ['forest_cicada.mp3'], 
      volume: 0.5, 
      folder: 'midi', 
      fadeInDuration: 4000, 
      fadeOutDuration: 4000 
    },
    
    // 🌞 Après-midi - 2 sons disponibles
    afternoon: { 
      sounds: ['birds-singing.mp3', 'summer-insects-243572.mp3'], 
      volume: 0.6, 
      folder: 'apres-midi', 
      fadeInDuration: 5000, 
      fadeOutDuration: 5000 
    },
    
    // 🌆 Coucher du soleil - 2 sons disponibles
    sunset: { 
      sounds: ['bird-chirp.mp3', 'grillon-drome.mp3'], 
      volume: 0.4, 
      folder: 'coucher-soleil', 
      fadeInDuration: 5000, 
      fadeOutDuration: 5000 
    },
};

// 🎵 Fonction pour construire l'URL du son
const getSoundUrl = (soundFile: string, folder: string): string => {
  return `/sounds/${folder}/${soundFile}`;
};

// 🎵 Fonction pour choisir un son aléatoire dans la liste
const getRandomSound = (sounds: string[]): string => {
  return sounds[Math.floor(Math.random() * sounds.length)];
};

const AmbientSoundManagerV2: React.FC<AmbientSoundManagerV2Props> = ({
  skyMode,
  enabled = true,
  volume = 1.0
}) => {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const fadeTween = useRef<gsap.core.Tween | null>(null);
  const currentSoundKey = useRef<string | null>(null);
  const currentSoundFile = useRef<string | null>(null);

  useEffect(() => {
    console.log(`🎵 AmbientSoundManagerV2: Mode=${skyMode}, Enabled=${enabled}, Volume=${volume}`);
    
    const newSoundConfig = SOUND_CONFIG[skyMode];
    const newSoundKey = skyMode;

    // --- Audio Enabled/Disabled Logic ---
    if (!enabled) {
      if (audioRef.current) {
        console.log(`🔇 Audio désactivé. Arrêt du son ${currentSoundKey.current}.`);
        if (fadeTween.current) fadeTween.current.kill();
        fadeTween.current = gsap.to(audioRef.current, {
          volume: 0,
          duration: 1, // Fade out rapide
          onComplete: () => {
            audioRef.current?.pause();
            audioRef.current = null;
            currentSoundKey.current = null;
            currentSoundFile.current = null;
            console.log("🔇 Audio arrêté et nettoyé.");
          }
        });
      }
      return; // Arrêter ici si l'audio est désactivé
    }

    // --- Sound Change Logic ---
    if (enabled && newSoundKey !== currentSoundKey.current) {
      console.log(`🎵 Changement de mode: ${currentSoundKey.current} → ${newSoundKey}`);
      
      // 1. Fade out de l'ancien son s'il existe
      const oldAudio = audioRef.current;
      if (oldAudio) {
        console.log(`🎵 Arrêt progressif de l'ancien son: ${currentSoundFile.current}`);
        if (fadeTween.current) fadeTween.current.kill();
        fadeTween.current = gsap.to(oldAudio, {
          volume: 0,
          duration: newSoundConfig?.fadeOutDuration / 1000 || 2.0,
          ease: "power1.inOut",
          onComplete: () => {
            oldAudio.pause();
            console.log(`🎵 Ancien son ${currentSoundFile.current} arrêté.`);
          }
        });
      }

      // 2. Démarrer le nouveau son si la configuration existe
      if (newSoundConfig && newSoundConfig.sounds.length > 0) {
        // Choisir un son aléatoire dans la liste
        const selectedSound = getRandomSound(newSoundConfig.sounds);
        const soundUrl = getSoundUrl(selectedSound, newSoundConfig.folder);
        
        console.log(`🎵 Démarrage du nouveau son: ${selectedSound} depuis ${newSoundConfig.folder}`);
        console.log(`🎵 URL: ${soundUrl}`);
        
        currentSoundKey.current = newSoundKey;
        currentSoundFile.current = selectedSound;
        
        const newAudio = new Audio(soundUrl);
        newAudio.loop = true;
        newAudio.volume = 0; // Commencer silencieux pour le fade in
        audioRef.current = newAudio;

        // Jouer puis faire le fade in
        newAudio.play().then(() => {
            console.log(`🎵 Fade in du nouveau son: ${selectedSound}`);
            if (fadeTween.current) fadeTween.current.kill();
            const targetVolume = newSoundConfig.volume * volume;
            fadeTween.current = gsap.to(newAudio, {
                volume: targetVolume,
                duration: newSoundConfig.fadeInDuration / 1000 || 2.0,
                ease: "power1.inOut",
            });
        }).catch(error => {
            console.error(`❌ Erreur lors de la lecture du son ${soundUrl}:`, error);
            audioRef.current = null;
            currentSoundKey.current = null;
            currentSoundFile.current = null;
        });
      } else {
        // Si le nouveau mode n'a pas de son, s'assurer qu'on est silencieux
        console.log(`🔇 Aucun son configuré pour le mode: ${newSoundKey}`);
        currentSoundKey.current = newSoundKey;
        currentSoundFile.current = null;
        audioRef.current = null;
      }
    }

    // --- Volume Adjustment Logic ---
    if (audioRef.current && enabled && newSoundKey === currentSoundKey.current && newSoundConfig) {
        const targetVolume = newSoundConfig.volume * volume;
        console.log(`🔊 Ajustement du volume: ${targetVolume.toFixed(2)}`);
        if (fadeTween.current) fadeTween.current.kill();
        fadeTween.current = gsap.to(audioRef.current, {
            volume: targetVolume,
            duration: 1.0, // Ajustement fluide du volume sur 1 seconde
            ease: "power1.inOut",
        });
    }

    // --- Cleanup on unmount ---
    return () => {
      console.log("🧹 Nettoyage AmbientSoundManagerV2 au démontage.");
      if (fadeTween.current) fadeTween.current.kill();
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, [skyMode, enabled, volume]); // Réagir aux changements de ces props

  return null; // Ce composant ne rend rien visuellement
};

export default AmbientSoundManagerV2;
